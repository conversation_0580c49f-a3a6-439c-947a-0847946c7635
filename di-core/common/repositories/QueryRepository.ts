/*
 * @author: tvc12 - Thi<PERSON> Vi
 * @created: 11/27/20, 10:30 AM
 */

import { BaseClient } from '@core/common/services/HttpClient';
import { SeriesOneItem, SeriesOneResponse, VisualizationResponse } from '@core/common/domain/response';
import { ExportType, QueryRequest } from '@core/common/domain/request';
import { PivotTableQuerySetting, QuerySetting, RawQuerySetting, TableQueryChartSetting, UserProfile } from '@core/common/domain';
import { JsonUtils, Log } from '@core/utils';

export abstract class QueryRepository {
  abstract query(request: QueryRequest): Promise<VisualizationResponse>;

  abstract export(request: QueryRequest, type: ExportType): Promise<Blob>;

  abstract queryWithUser(request: QueryRequest, userProfile: UserProfile): Promise<VisualizationResponse>;
}

export class QueryRepositoryImpl implements QueryRepository {
  constructor(private baseClient: BaseClient) {}

  query(request: QueryRequest): Promise<VisualizationResponse> {
    const jsonParser: ((data: string) => any) | undefined = this.getJsonParser(request.querySetting);
    return this.baseClient.post(`/chart/query`, request, void 0, void 0, jsonParser).then(obj => VisualizationResponse.fromObject(obj));
  }

  queryWithUser(request: QueryRequest, userProfile: UserProfile): Promise<VisualizationResponse> {
    const jsonParser: ((data: string) => any) | undefined = this.getJsonParser(request.querySetting);
    return this.baseClient
      .post(`/chart/view_as`, { queryRequest: request, userProfile: userProfile }, void 0, void 0, jsonParser)
      .then(obj => VisualizationResponse.fromObject(obj));
  }

  /**
   * parse json for table & pivot table
   */
  private getJsonParser(querySetting: QuerySetting): ((data: string) => any) | undefined {
    if (
      PivotTableQuerySetting.isPivotChartSetting(querySetting) ||
      TableQueryChartSetting.isTableChartSetting(querySetting) ||
      RawQuerySetting.isRawQuerySetting(querySetting)
    ) {
      return (data: any) => {
        const records = data.records;
        delete data.records;
        const newData = JsonUtils.fromObject(data);
        return Object.assign(newData, { records: records });
      };
    } else {
      return void 0;
    }
  }

  async export(request: QueryRequest, type: ExportType): Promise<Blob> {
    return this.baseClient.post<Blob>(`/query/${type}`, request, void 0, {}, data => data, void 0, {
      responseType: 'blob'
    });
  }
}

export class MockQueryRepo extends QueryRepositoryImpl {
  query(request: QueryRequest): Promise<VisualizationResponse> {
    return Promise.resolve(
      new SeriesOneResponse(
        [
          new SeriesOneItem(
            'Temperatures',
            [
              [-9.5, 8.0],
              [-7.8, 8.3],
              [-13.1, 9.2],
              [-4.4, 15.7],
              [-1.0, 20.8],
              [3.1, 28.4],
              [8.9, 27.0],
              [9.6, 23.0],
              [4.9, 19.3],
              [-5.2, 11.6],
              [-10.5, 12.0],
              [-12.1, 8.5]
            ],
            0
          )
        ],
        ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
      )
    );
  }
}
