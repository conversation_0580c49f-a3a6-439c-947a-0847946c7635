import { BaseModule } from '@core/common/modules/Module';
import { Container } from 'typescript-ioc';
import { DIKeys } from '@core/common/modules/Di';
import { BaseClient } from '@core/common/services';
import { ClientBuilders } from '@core/common/misc/ClientBuilder';

export class HttpModule extends BaseModule {
  configuration(): void {
    const timeout: number = window.appConfig?.REQUEST_TIME_OUT_MS || 30000;
    const caasApiUrl = window.appConfig?.CAAS_API_URL;
    Container.bindName(DIKeys.CaasClient).to(this.buildClient(caasApiUrl, timeout));

    const biApiUrl = window.appConfig.BI_API_URL;
    Container.bindName(DIKeys.BiClient).to(this.buildClient(biApiUrl, timeout));

    const schemaApiUrl = window.appConfig.SCHEMA_API_URL;
    Container.bindName(DIKeys.SchemaClient).to(this.buildClient(schemaApiUrl, timeout));

    const cdpApiUrl = window.appConfig.CDP_API_URL;
    Container.bindName(DIKeys.CdpClient).to(this.buildClient(cdpApiUrl, timeout));

    const staticApiUrl = window.appConfig.UPLOAD_STATIC_API_URL;
    Container.bindName(DIKeys.StaticClient).to(this.buildClient(staticApiUrl, timeout));

    const cookApiUrl = window.appConfig.DATA_COOK_API_URL;
    Container.bindName(DIKeys.DataCookClient).to(this.buildClient(cookApiUrl, timeout));

    const billingApiUrl = window.appConfig.BILLING_API_URL;
    Container.bindName(DIKeys.BillingClient).to(this.buildClient(billingApiUrl, timeout));

    const workerApiUrl = window.appConfig.JOB_WORKER_API_URL;
    Container.bindName(DIKeys.WorkerClient).to(this.buildClient(workerApiUrl, timeout));

    const schedulerApiUrl = window.appConfig.JOB_SCHEDULER_API_URL;
    Container.bindName(DIKeys.SchedulerClient).to(this.buildClient(schedulerApiUrl, timeout));

    const relayApiUrl = window.appConfig.RELAY_API_URL;
    Container.bindName(DIKeys.RelayClient).to(this.buildClient(relayApiUrl, timeout));

    const openApiUrl = window.appConfig.OPEN_API_URL;
    Container.bindName(DIKeys.OpenAiClient).to(this.buildWithoutAuthenticationClient(openApiUrl, timeout));
  }

  private buildClient(apiUrl: string, timeout: number): BaseClient {
    return ClientBuilders.authAndTokenBuilder()
      .withBaseUrl(apiUrl)
      .withTimeout(timeout)
      .build();
  }

  private buildWithoutAuthenticationClient(apiUrl: string, timeout: number): BaseClient {
    return ClientBuilders.defaultBuilder()
      .withBaseUrl(apiUrl)
      .withTimeout(timeout)
      .build();
  }
}
