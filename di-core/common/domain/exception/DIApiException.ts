import DIException from '@core/common/domain/exception/DIException';
import { get } from 'lodash';
import { StringUtils } from '@/utils';

export class DIApiException extends DIException {
  constructor(message: string, statusCode?: number, reason?: string) {
    super(message, statusCode, reason);
  }

  static fromObject(object: any): DIApiException {
    if (DIApiException.isNewFormat(object)) {
      return new DIApiException(object.error.message, object.error.code, object.error.reason);
    } else if (DIApiException.isOpenAIFormat(object)) {
      return new DIApiException(object.error.message, -1, object.error.code);
    } else {
      return new DIApiException(object.message ?? object.msg, object.code, object.reason);
    }
  }

  /**
   * new format is like this:
   * {
   * 	"success": false,
   * 	"error": {
   * 		"code": 500,
   * 		"reason": "internal_error",
   * 		"message": "Internal Server Error"
   * 	}
   * }
   */
  protected static isNewFormat(object: any): boolean {
    return object.success === false && object.error;
  }

  protected static isOpenAIFormat(object: any): boolean {
    // return StringUtils.isNotEmpty(get(object, 'error.message', ''));
    const message = get(object, 'error.message', '');
    return message.length > 0;
  }
}
