/*
 * @author: tvc12 - Thi<PERSON> Vi
 * @created: 5/30/21, 9:46 PM
 */

import { ChartOption } from '@core/common/domain/model/chart-option/ChartOption';
import { ChartOptionData, SeriesOptionData, ChartOptionClassName } from '@core/common/domain/model';

export class PieChartOption extends ChartOption<SeriesOptionData> {
  static readonly DEFAULT_SETTING = {
    plotOptions: {
      pie: {
        borderWidth: 0,
        borderColor: 'black',
        dataLabels: {
          borderWidth: 0,
          textOutline: '0px contrast',
          useHTML: true,
          style: {
            border: '0px transparent',
            borderColor: 'none',
            textShadow: false,
            outline: 'none'
          }
        }
      }
    }
  };

  readonly className = ChartOptionClassName.PieSetting;

  constructor(options: ChartOptionData = {}) {
    super(options);
  }

  static fromObject(obj: PieChartOption): PieChartOption {
    return new PieChartOption(obj.options);
  }

  static getDefaultChartOption(): PieChartOption {
    const textColor = this.getPrimaryTextColor();
    const options: SeriesOptionData = {
      legend: {
        enabled: true,
        verticalAlign: 'bottom',
        layout: 'horizontal',
        itemStyle: {
          color: textColor
        },
        title: {
          text: '',
          enabled: true,
          style: {
            color: textColor
          }
        }
      },
      themeColor: { enabled: true },
      tooltip: {
        backgroundColor: this.getTooltipBackgroundColor(),
        style: ChartOption.getSecondaryStyle()
      },
      title: ChartOption.getDefaultTitle(),
      subtitle: ChartOption.getDefaultSubtitle(),
      plotOptions: {
        pie: {
          dataLabels: {
            enabled: true,
            style: {
              color: textColor
            }
          }
        }
      },
      affectedByFilter: true,
      isCrossFilter: true,
      background: this.getThemeBackgroundColor()
    };
    return new PieChartOption(options);
  }

  static getDefaultSemiPieOption(): PieChartOption {
    const textColor = this.getPrimaryTextColor();
    const options: SeriesOptionData = {
      legend: {
        enabled: false,
        verticalAlign: 'bottom',
        layout: 'horizontal',
        itemStyle: {
          color: textColor
        },
        title: {
          text: '',
          enabled: true,
          style: {
            color: textColor
          }
        }
      },
      themeColor: { enabled: true },
      tooltip: {
        backgroundColor: this.getTooltipBackgroundColor(),
        style: ChartOption.getSecondaryStyle()
      },
      title: ChartOption.getDefaultTitle(),
      subtitle: ChartOption.getDefaultSubtitle(),
      plotOptions: {
        pie: {
          startAngle: -90,
          endAngle: 90,
          center: ['50%', '100%'],
          size: '150%',
          innerSize: '50%',
          dataLabels: {
            enabled: true,
            style: {
              color: textColor
            }
          }
        }
      },
      isSemiPie: true,
      affectedByFilter: true,
      isCrossFilter: true,
      background: this.getThemeBackgroundColor()
    };
    return new PieChartOption(options);
  }
}
