/*
 * @author: tvc12 - Thi<PERSON> Vi
 * @created: 5/30/21, 9:47 PM
 */

import { ChartOption } from '@core/common/domain/model/chart-option/ChartOption';
import Highcharts from 'highcharts/highmaps';
import { ChartOptionData, ChartOptionClassName } from '@core/common/domain/model';
import { ChartType } from '@/shared';

/**
 * @deprecated from v1.0.
 */
export class DrilldownChartOption extends ChartOption {
  static readonly DEFAULT_SETTING: Highcharts.Options = {
    yAxis: {
      gridLineWidth: 0.5,
      gridLineColor: 'var(--grid-line-color)',
      tickLength: 0
      // gridLineDashStyle: 'longdash'
    },
    xAxis: {
      lineWidth: 0.5
    },
    plotOptions: {
      series: {
        threshold: null as any,
        borderWidth: 0
        // dataLabels: {
        //   color: '#fff',
        //   useHTML: true,
        //   style: {
        //     border: '0px',
        //     textShadow: false,
        //     borderColor: 'none'
        //   }
        // }
      }
    }
  };

  className = ChartOptionClassName.DrilldownSetting;

  constructor(options: ChartOptionData = {}) {
    super(options);
  }

  static fromObject(obj: DrilldownChartOption): DrilldownChartOption {
    return new DrilldownChartOption(obj.options);
  }

  private static toHighchartsType(chartType: ChartType): string {
    switch (chartType) {
      case ChartType.BarDrillDown:
        return 'bar';
      case ChartType.ColumnDrillDown:
        return 'column';
      default:
        return 'column';
    }
  }

  static getDefaultChartOption(chartType: ChartType): DrilldownChartOption {
    const textColor = this.getPrimaryTextColor();
    const gridLineColor: string = this.getGridLineColor();
    return new DrilldownChartOption({
      chart: {
        type: this.toHighchartsType(chartType)
      },
      legend: {
        enabled: false
      },
      textColor: textColor,
      title: ChartOption.getDefaultTitle(),
      subtitle: ChartOption.getDefaultSubtitle(),
      affectedByFilter: true,
      themeColor: { enabled: true },
      background: this.getThemeBackgroundColor(),
      plotOptions: {
        series: {
          lineWidth: 2,
          dashStyle: 'Solid',
          marker: {
            enabled: false
          },
          dataLabels: {
            enabled: false,
            style: {
              ...ChartOption.getSecondaryStyle(),
              color: textColor,
              textOutline: 0
            }
          }
        }
      },
      xAxis: [
        {
          visible: true,
          labels: {
            style: {
              color: textColor
            }
          },
          title: {
            enabled: true,
            style: {
              color: textColor
            },
            text: ''
          },
          gridLineWidth: '0',
          gridLineColor: gridLineColor
        }
      ],
      yAxis: [
        {
          visible: true,
          labels: {
            style: {
              color: textColor
            }
          },
          title: {
            enabled: true,
            style: {
              color: textColor
            },
            text: ''
          },
          gridLineWidth: '0.5',
          gridLineColor: gridLineColor
        },
        {
          visible: false,
          labels: {
            style: {
              color: textColor
            }
          },
          title: {
            enabled: true,
            style: {
              color: textColor
            },
            text: 'Dual axis title'
          },
          gridLineWidth: '0.5',
          gridLineColor: gridLineColor
        }
      ],
      tooltip: {
        backgroundColor: this.getTooltipBackgroundColor(),
        style: {
          color: textColor,
          fontFamily: ChartOption.getSecondaryFontFamily()
        }
      }
    });
  }
}
