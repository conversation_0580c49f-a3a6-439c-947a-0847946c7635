export enum ChartOptionClassName {
  TableSetting = 'table_chart_setting',
  SeriesSetting = 'highcharts_series_chart_setting',
  PieSetting = 'highcharts_pie_chart_setting',
  FunnelSetting = 'highcharts_funnel_chart_setting',
  PyramidSetting = 'highcharts_pyramid_chart_setting',
  /**
   * @deprecated from v1.0.0
   */
  DrilldownSetting = 'highcharts_drilldown_chart_setting',
  /**
   * @deprecated from v1.0.0
   */
  DrilldownPieSetting = 'highcharts_drilldown_pie_chart_setting',
  GaugeSetting = 'highcharts_gauge_chart_setting',
  HeatMapSetting = 'highcharts_heat_map_chart_setting',
  TreeMapSetting = 'highcharts_tree_map_chart_setting',
  BubbleSetting = 'highcharts_bubble_chart_setting',
  ScatterSetting = 'highcharts_scatter_chart_setting',
  ParetoSetting = 'highcharts_pareto_chart_setting',
  HistogramSetting = 'highcharts_histogram_chart_setting',
  /**
   * @deprecated from v1.0.3
   */
  BellCurveSetting = 'highcharts_bell_curve_chart_setting',
  BellCurve2Setting = 'highcharts_bell_curve_2_chart_setting',
  NumberSetting = 'number_chart_setting',
  WordCloudSetting = 'word_cloud_chart_setting',
  StackedSeriesSetting = 'stacking_series_chart_setting',
  CircularBarSetting = 'circular_bar_chart_setting',
  DropdownSetting = 'dropdown_filter_chart_setting',
  TabFilterSetting = 'tab_filter_chart_setting',
  MapSetting = 'highcharts_map_chart_setting',
  PivotTableSetting = 'pivot_table_chart_setting',
  ParliamentSetting = 'parliament_setting',
  SpiderWebSetting = 'spider_web_chart_setting',
  SankeySetting = 'sankey_setting',
  SlicerFilterSetting = 'slice_filter_setting',
  DateSelectFilterSetting = 'date_select_filter_setting',
  InputFilterSetting = 'input_filter_setting',
  FlattenTableSetting = 'flatten_table_setting',
  FlattenPivotTableSetting = 'flatten_pivot_table_setting',
  BulletSetting = 'bullet_graph_setting',
  WindRoseSetting = 'wind_rose_setting',
  LineStockSetting = 'line_stock_setting',
  /**
   * @deprecated from v1.0.0
   */
  InputControlSetting = 'input_control_setting',
  TreeFilterSetting = 'tree_filter_chart_setting',
  DonutSetting = 'donut_chart_setting',
  VariablePieSetting = 'variablepie_chart_setting',
  GenericSetting = 'generic_chart_setting'
}
