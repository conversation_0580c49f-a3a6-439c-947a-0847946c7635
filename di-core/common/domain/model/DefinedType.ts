export type DashboardId = number;
export type DirectoryId = number;
export type WidgetId = number;
export type TabId = number;
export type Id = number;
export type UserId = string;
export type DDate = number;
export type DIMap<T> = { [idx: number]: T };
export type Geocode = string;
export type SourceId = number;
export type JobId = number;
export type OrgId = number;
export type SyncId = number;
export type Gender = number;
export type CohortId = number;
export type PolicyId = number;
