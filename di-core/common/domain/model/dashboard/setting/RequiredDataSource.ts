// /* eslint-disable @typescript-eslint/no-use-before-define */
// import { DataSourceType } from '@core/data-ingestion/domain/data-source/DataSourceType';
//
// export class RequiredDataSource {
//   type: DataSourceType;
//   setting: RequiredDataSourceSetting;
//   originDatabaseName: string;
//
//   constructor(data: { type: DataSourceType; originDatabaseName: string; setting?: RequiredDataSourceSetting }) {
//     this.type = data.type;
//     this.setting = data.setting ? RequiredDataSourceSetting.fromObject(data.setting) : RequiredDataSourceSetting.createDefault();
//     this.originDatabaseName = data.originDatabaseName;
//   }
//
//   static fromObject(data: any): RequiredDataSource {
//     return new RequiredDataSource(data);
//   }
//
//   get isConnected(): boolean {
//     return this.setting.isConnected;
//   }
//
//   set isConnected(value: boolean) {
//     this.setting.isConnected = value;
//   }
//
//   get sourceId(): number | undefined {
//     return this.setting.sourceId;
//   }
//
//   set sourceId(value: number | undefined) {
//     this.setting.sourceId = value;
//   }
//
//   get jobIds(): number[] {
//     return this.setting.jobIds;
//   }
//
//   set jobIds(value: number[]) {
//     this.setting.jobIds = value;
//   }
//
//   get destDatabaseName(): string | undefined {
//     return this.setting.destDatabaseName;
//   }
//
//   set destDatabaseName(value: string | undefined) {
//     this.setting.destDatabaseName = value;
//   }
// }
//
// export class RequiredDataSourceSetting {
//   isConnected: boolean;
//   sourceId: number | undefined;
//   jobIds: number[];
//   destDatabaseName: string | undefined;
//
//   constructor(data: { isConnected: boolean; sourceId?: number; jobIds: number[]; destDatabaseName?: string }) {
//     this.isConnected = data.isConnected;
//     this.sourceId = data.sourceId;
//     this.jobIds = data.jobIds;
//     this.destDatabaseName = data.destDatabaseName;
//   }
//
//   static fromObject(data: any): RequiredDataSourceSetting {
//     return new RequiredDataSourceSetting(data);
//   }
//
//   static createDefault(): RequiredDataSourceSetting {
//     return new RequiredDataSourceSetting({
//       isConnected: false,
//       jobIds: []
//     });
//   }
// }
