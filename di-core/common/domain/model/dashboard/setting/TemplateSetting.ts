// /* eslint-disable @typescript-eslint/no-use-before-define */
//
// import { DataSourceType } from '@core/data-ingestion';
// import { RequiredDataSource } from './RequiredDataSource';
//
// export class TemplateSetting {
//   requiredDatasourceList: RequiredDataSource[];
//
//   constructor(data: { requiredDatasourceList?: RequiredDataSource[] }) {
//     this.requiredDatasourceList = data.requiredDatasourceList?.map((item: any) => RequiredDataSource.fromObject(item)) ?? [];
//   }
//
//   static fromObject(data: any): TemplateSetting {
//     return new TemplateSetting(data);
//   }
//
//   getNotConnectedDataSourceList(): RequiredDataSource[] {
//     return this.requiredDatasourceList.filter(item => !item.isConnected);
//   }
//
//   static createDefault(): TemplateSetting {
//     return new TemplateSetting({
//       requiredDatasourceList: []
//     });
//   }
//
//   setConnected(type: DataSourceType, sourceId: number, jobIds: number[], destDatabaseName: string): void {
//     const dataSourceSetting = this.requiredDatasourceList.find(item => item.type === type);
//     if (dataSourceSetting) {
//       dataSourceSetting.isConnected = true;
//       dataSourceSetting.sourceId = sourceId;
//       dataSourceSetting.jobIds = jobIds;
//       dataSourceSetting.destDatabaseName = destDatabaseName;
//     }
//   }
// }
