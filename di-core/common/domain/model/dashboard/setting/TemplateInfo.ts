// import { MapUtils } from '@/utils';
// import { TemplateSetting } from './TemplateSetting';
//
// export class TemplateInfo {
//   fromTemplateId: number;
//   isComplete: boolean;
//   setting: TemplateSetting;
//   extraData: Map<string, any>;
//
//   constructor(data: { fromTemplateId: number; isComplete: boolean; setting: TemplateSetting; extraData?: Map<string, any> }) {
//     this.fromTemplateId = data.fromTemplateId;
//     this.isComplete = data.isComplete;
//     this.setting = TemplateSetting.fromObject(data.setting);
//     this.extraData = MapUtils.newMap(data.extraData);
//   }
//
//   static fromObject(data: any): TemplateInfo {
//     return new TemplateInfo(data);
//   }
//
//   static createDefault(): TemplateInfo {
//     return new TemplateInfo({
//       fromTemplateId: 0,
//       isComplete: false,
//       setting: TemplateSetting.createDefault()
//     });
//   }
//
//   withCompleteSetup(): this {
//     this.isComplete = true;
//     return this;
//   }
// }
