/*
 * @author: tvc12 - <PERSON><PERSON><PERSON> Vi
 * @created: 12/17/20, 11:49 AM
 */

import { ConditionData, ConfigType, FunctionData } from '@/shared';
import { Id, StyleSettings, WidgetId } from '@core/common/domain/model';

export interface WidgetExtraData {
  configs?: Record<ConfigType, FunctionData[]>;
  filters?: Record<Id, ConditionData[]>;
  currentChartType: string;
  styleSettings?: StyleSettings;
  origin?: WidgetId; //Origin Widget Before Duplicate
}
