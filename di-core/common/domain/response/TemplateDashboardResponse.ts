export class TemplateDashboardResponse {
  id: number;
  name: string;
  description: string;
  thumbnail: string;

  constructor(data: { id: number; name: string; description: string; thumbnail: string }) {
    this.id = data.id;
    this.name = data.name;
    this.description = data.description;
    this.thumbnail = data.thumbnail;
  }

  static fromObject(obj: any): TemplateDashboardResponse {
    return new TemplateDashboardResponse(obj);
  }
}
