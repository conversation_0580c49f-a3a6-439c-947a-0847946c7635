// import { MapUtils } from '@/utils';
// import { DataSourceType, JobStatus } from '@core/data-ingestion';
// import { DIException } from '../exception';
//
// export class DataSourceConnectionStatus {
//   type: DataSourceType;
//   isConnected: boolean;
//   isDatabaseExist: boolean;
//   isSourceExist: boolean;
//   jobStatusMap: Map<number, JobStatus>;
//
//   constructor(data: { type: DataSourceType; isConnected: boolean; isDatabaseExist: boolean; isSourceExist: boolean; jobStatusMap: Map<number, JobStatus> }) {
//     this.type = data.type;
//     this.isConnected = data.isConnected;
//     this.isDatabaseExist = data.isDatabaseExist;
//     this.isSourceExist = data.isSourceExist;
//     this.jobStatusMap = MapUtils.newMap(data.jobStatusMap);
//   }
//
//   isOk(): boolean {
//     return this.isConnected && this.isDatabaseExist && this.isSourceExist && this.isAllRunCompleted();
//   }
//
//   isError(): boolean {
//     return !this.isConnected || !this.isSourceExist || this.hasJobError();
//   }
//
//   getError(): DIException {
//     if (!this.isConnected) {
//       return new DIException(`Cannot connect to data source ${this.type}`);
//     }
//
//     if (!this.isSourceExist) {
//       return new DIException(`Data source ${this.type} does not exist, please connect to data source again`);
//     }
//
//     if (this.hasJobError()) {
//       return new DIException(`Sync data to data source ${this.type} failed, please check your data source connection and try again`);
//     }
//     return new DIException(`Unknown error in this dashboard, please refresh the page and try again.`);
//   }
//
//   hasJobError(): boolean {
//     return Array.from(this.jobStatusMap.values()).some(status => status === JobStatus.Error);
//   }
//
//   private isAllRunCompleted(): boolean {
//     return Array.from(this.jobStatusMap.values()).every(status => status === JobStatus.Synced || status === JobStatus.Error || status === JobStatus.Terminated);
//   }
//
//   static fromObject(data: any): DataSourceConnectionStatus {
//     return new DataSourceConnectionStatus({
//       type: data.type,
//       isConnected: data.isConnected,
//       isDatabaseExist: data.isDatabaseExist,
//       isSourceExist: data.isSourceExist,
//       jobStatusMap: MapUtils.toMap(data.jobStatusMap)
//     });
//   }
// }
