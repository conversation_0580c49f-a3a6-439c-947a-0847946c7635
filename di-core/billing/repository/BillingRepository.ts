import { PaymentMethod, PaymentStatus, ProductInfo, ProductSubscription, ProductSubscriptionInfo, UnknownPaymentInfo } from '@core/billing';
import { InjectValue } from 'typescript-ioc';
import { DIKeys } from '@core/common/modules';
import { BaseClient } from '@core/common/services';
import { PlanType } from '@core/organization/domain/Plan/PlanType';

export abstract class BillingRepository {
  abstract getProducts(): Promise<ProductInfo[]>;

  abstract subscribeProduct(licenseKey: string, planType: PlanType): Promise<ProductSubscriptionInfo>;

  abstract getSubscriptionInfo(licenseKey: string): Promise<ProductSubscriptionInfo>;

  abstract cancelSubscription(licenseKey: string): Promise<ProductSubscriptionInfo>;

  abstract redeemCode(licenseKey: string, code: string): Promise<boolean>;
}

export class BillingRepositoryImpl extends BillingRepository {
  @InjectValue(DIKeys.BillingClient)
  private readonly client!: BaseClient;

  getProducts(): Promise<ProductInfo[]> {
    return this.client.get<ProductInfo[]>(`/plan`).then(res => {
      const products = res.map(product => ProductInfo.fromObject(product));
      return products;
    });
  }

  subscribeProduct(licenseKey: string, planType: PlanType): Promise<ProductSubscriptionInfo> {
    return this.client
      .put<ProductSubscriptionInfo>(`/plan/${licenseKey}`, { planType })
      .then(res => ProductSubscriptionInfo.fromObject(res));
  }

  getSubscriptionInfo(licenseKey: string): Promise<ProductSubscriptionInfo> {
    return this.client.get<ProductSubscriptionInfo>(`/plan/${licenseKey}`).then(res => ProductSubscriptionInfo.fromObject(res));
  }

  cancelSubscription(licenseKey: string): Promise<ProductSubscriptionInfo> {
    return this.client.put<ProductSubscriptionInfo>(`/plan/${licenseKey}/cancel`).then(res => ProductSubscriptionInfo.fromObject(res));
  }

  async redeemCode(licenseKey: string, code: string): Promise<boolean> {
    const response: any = await this.client.post(`/licenses/${licenseKey}/redeem`, { code: code });
    return response.status;
  }
}

export class MockBillingRepository extends BillingRepository {
  cancelSubscription(licenseKey: string): Promise<ProductSubscriptionInfo> {
    return Promise.resolve(
      new ProductSubscriptionInfo(
        new ProductInfo('start_up', PlanType.Startup, 'Parturienttortor', 0, 1000, -1),
        new ProductSubscription('start_up', licenseKey, 'start_up', 'mock_payment_id', -1, -1, -1, -1),
        new UnknownPaymentInfo(PaymentMethod.Unknown, PaymentStatus.Canceled)
      )
    );
  }

  getProducts(): Promise<ProductInfo[]> {
    return Promise.resolve([]);
  }

  getSubscriptionInfo(licenseKey: string): Promise<ProductSubscriptionInfo> {
    return Promise.resolve(
      new ProductSubscriptionInfo(
        new ProductInfo('start_up', PlanType.Startup, 'Parturienttortor', 0, 1000, -1),
        new ProductSubscription('start_up', licenseKey, 'start_up', 'mock_payment_id', -1, -1, -1, -1),
        new UnknownPaymentInfo(PaymentMethod.Unknown, PaymentStatus.BillingApproval)
      )
    );
  }

  redeemCode(licenseKey: string, code: string): Promise<boolean> {
    return Promise.resolve(true);
  }

  subscribeProduct(licenseKey: string, planType: PlanType): Promise<ProductSubscriptionInfo> {
    return Promise.resolve(
      new ProductSubscriptionInfo(
        new ProductInfo('start_up', PlanType.Startup, 'Parturienttortor', 0, 1000, -1),
        new ProductSubscription('start_up', licenseKey, 'start_up', 'mock_payment_id', -1, -1, -1, -1),
        new UnknownPaymentInfo(PaymentMethod.Unknown, PaymentStatus.Succeeded)
      )
    );
  }
}
