import { BaseModule } from '@core/common/modules';
import { <PERSON><PERSON><PERSON>, <PERSON>ope } from 'typescript-ioc';
import { BillingRepository, BillingRepositoryImpl, BillingService, BillingServiceImpl, MockBillingRepository } from '@core/billing';

export class BillingModule extends BaseModule {
  configuration() {
    this.bindBillingRepository();

    Container.bind(BillingService)
      .to(BillingServiceImpl)
      .scope(Scope.Singleton);
  }

  private bindBillingRepository() {
    const enableBilling = window.appConfig.IS_ENABLE_BILLING ?? true;
    if (enableBilling) {
      Container.bind(BillingRepository)
        .to(BillingRepositoryImpl)
        .scope(Scope.Singleton);
    } else {
      Container.bind(BillingRepository)
        .to(MockBillingRepository)
        .scope(Scope.Singleton);
    }
  }
}
