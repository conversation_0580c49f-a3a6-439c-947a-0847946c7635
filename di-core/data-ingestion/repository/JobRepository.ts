/*
 * @author: tvc12 - Thi<PERSON> Vi
 * @created: 6/1/21, 2:03 PM
 */

import { JobId, ListingRequest } from '@core/common/domain';
import { DIKeys } from '@core/common/modules';
import { BaseClient } from '@core/common/services/HttpClient';
import { JobStatus, ListingResponse } from '@core/data-ingestion';
import { Job, JobInfo } from '@core/data-ingestion/domain/job/Job';
import { BaseResponse } from '@core/data-ingestion/domain/response/BaseResponse';
import { ForceMode } from '@core/lake-house/domain/lake-job/ForceMode';
import { InjectValue } from 'typescript-ioc';

const headerScheduler = {
  'Content-Type': 'application/json',
  'access-token': 'job$<EMAIL>'
};

export abstract class JobRepository {
  abstract create(request: Job): Promise<JobInfo>;

  abstract multiCreate(request: Job, tables: string[]): Promise<boolean>;

  abstract list(request: ListingRequest, currentStatuses?: JobStatus[]): Promise<ListingResponse<JobInfo>>;

  abstract delete(id: JobId): Promise<boolean>;

  abstract multiDelete(ids: JobId[]): Promise<boolean>;
  abstract update(id: JobId, job: Job): Promise<boolean>;

  abstract testConnection(job: Job): Promise<BaseResponse>;

  abstract forceSync(jobId: JobId, date: number, mode: ForceMode): Promise<BaseResponse>;

  abstract multiForceSync(jobIds: JobId[], date: number, mode: ForceMode): Promise<Record<JobId, boolean>>;

  abstract cancel(jobId: JobId): Promise<BaseResponse>;

  abstract multiCreateV2(jobs: Job[], isRunNow: boolean): Promise<Job[]>;
}

export class JobRepositoryImpl extends JobRepository {
  @InjectValue(DIKeys.SchedulerClient)
  private readonly httpClient!: BaseClient;

  create(request: Job): Promise<JobInfo> {
    return this.httpClient.post(`job/create`, { job: request }, void 0, headerScheduler).then(jobInfo => JobInfo.fromObject(jobInfo));
  }

  list(request: ListingRequest, currentStatuses?: JobStatus[]): Promise<ListingResponse<JobInfo>> {
    return this.httpClient
      .post<any>(`job/list`, { ...request, currentStatuses: currentStatuses }, void 0, headerScheduler)
      .then(response => new ListingResponse<JobInfo>(this.parseToListJob(response.data), response.total));
  }

  delete(jobId: JobId): Promise<boolean> {
    return this.httpClient.delete<boolean>(`job/${jobId}`, void 0, void 0, headerScheduler);
  }

  multiDelete(ids: JobId[]): Promise<boolean> {
    return this.httpClient.delete<boolean>(`job/multi_delete`, { ids: ids }, void 0, headerScheduler);
  }

  update(id: JobId, job: Job): Promise<boolean> {
    return this.httpClient.put<boolean>(`job/${id}`, { job: job }, void 0, headerScheduler);
  }

  testConnection(job: Job): Promise<BaseResponse> {
    return this.httpClient.post(`worker/job/test`, job, void 0, headerScheduler);
  }

  private parseToListJob(listObjects: any[]): JobInfo[] {
    return listObjects.map(obj => JobInfo.fromObject(obj));
  }

  forceSync(jobId: JobId, date: number, mode: ForceMode): Promise<BaseResponse> {
    return this.httpClient.put(`schedule/job/${jobId}/now`, { atTime: date, mode: mode }, void 0, headerScheduler);
  }

  multiForceSync(jobIds: JobId[], date: number, mode: ForceMode): Promise<Record<JobId, boolean>> {
    return this.httpClient.put(`schedule/job/multi_sync/now`, { atTime: date, mode: mode, ids: jobIds }, void 0, headerScheduler);
  }

  cancel(jobId: JobId): Promise<BaseResponse> {
    return this.httpClient.put(`schedule/job/${jobId}/kill`, {}, void 0, headerScheduler);
  }

  multiCreate(request: Job, tables: string[]): Promise<boolean> {
    return this.httpClient.post(`job/multi_create`, { baseJob: request, tableNames: tables }, void 0, headerScheduler);
  }

  multiCreateV2(jobs: Job[], isRunNow: boolean): Promise<Job[]> {
    return this.httpClient
      .post<BaseResponse>(`job/multi_create_jobs`, { jobs, isRunNow }, void 0, headerScheduler)
      .then((jobs: any) => jobs.map((job: any) => Job.fromObject(job)));
  }
}
