import { SchedulerOnce, TimeScheduler } from '@/screens/data-ingestion/components/job-scheduler-form/scheduler-time';
import { SchedulerName } from '@/shared';
import { JobId, SourceId } from '@core/common/domain';
import { DataDestination, DataSourceInfo, Job, JobStatus, JobType, MultiJob, PalexyDateRange, SyncMode, YOUTUBE_TABLE_INFOS } from '@core/data-ingestion';
import { JobName } from '@core/data-ingestion/domain/job/JobName';
import { cloneDeep } from 'lodash';

export class YoutubeJob implements Job, MultiJob {
  className = JobName.YoutubeJob;
  displayName: string;
  destDatabaseName: string;
  destTableName: string;
  destinations: DataDestination[];
  jobType = JobType.Youtube;
  jobId: JobId;
  orgId: string;
  sourceId: SourceId;
  lastSuccessfulSync: number;
  syncIntervalInMn: number;
  scheduleTime: TimeScheduler;
  nextRunTime: number;
  lastSyncStatus: JobStatus;
  currentSyncStatus: JobStatus;
  syncMode: SyncMode;
  lastSyncedValue: string;

  channelId: string;
  dateRange: PalexyDateRange;
  metrics: string[];
  dimensions: string[];
  filters: string[];
  sorts: string[];
  tableName: string;
  maxResults: number;

  constructor(
    jobId: JobId,
    orgId: string,
    sourceId: SourceId,
    displayName: string,
    destDatabaseName: string,
    destTableName: string,
    destinations: DataDestination[],
    lastSuccessfulSync: number,
    lastSyncedValue: string,
    syncIntervalInMn: number,
    scheduleTime: TimeScheduler,
    nextRunTime: number,
    lastSyncStatus: JobStatus,
    currentSyncStatus: JobStatus,
    dateRange: PalexyDateRange,
    channelId: string,
    metrics: string[],
    dimensions: string[],
    filters: string[],
    sorts: string[],
    tableName: string,
    maxResults: number,
    syncMode?: SyncMode
  ) {
    this.jobId = jobId;
    this.orgId = orgId;
    this.sourceId = sourceId;
    this.displayName = displayName;
    this.destDatabaseName = destDatabaseName;
    this.destTableName = destTableName;
    this.destinations = destinations;
    this.lastSuccessfulSync = lastSuccessfulSync;
    this.lastSyncedValue = lastSyncedValue;
    this.syncIntervalInMn = syncIntervalInMn;
    this.scheduleTime = scheduleTime;
    this.nextRunTime = nextRunTime;
    this.lastSyncStatus = lastSyncStatus;
    this.currentSyncStatus = currentSyncStatus;

    this.dateRange = dateRange;
    this.channelId = channelId;
    this.metrics = metrics;
    this.dimensions = dimensions;
    this.filters = filters;
    this.sorts = sorts;
    this.tableName = tableName;

    this.syncMode = syncMode || SyncMode.FullSync;
    this.maxResults = maxResults;
  }

  static fromObject(obj: any): YoutubeJob {
    return new YoutubeJob(
      obj.jobId,
      obj.orgId,
      obj.sourceId,
      obj.displayName,
      obj.destDatabaseName,
      obj.destTableName,
      obj.destinations,
      obj.lastSuccessfulSync,
      obj.lastSyncedValue,
      obj.syncIntervalInMn,
      obj.scheduleTime,
      obj.nextRunTime,
      obj.lastSyncStatus,
      obj.currentSyncStatus,
      PalexyDateRange.fromObject(obj.dateRange),
      obj.channelId,
      obj.metrics ?? [],
      obj.dimensions ?? [],
      obj.filters ?? [],
      obj.sorts ?? [],
      obj.tableName,
      obj.syncMode,
      obj.maxResults ?? 1000
    );
  }
  //
  static default(source?: DataSourceInfo): YoutubeJob {
    return new YoutubeJob(
      Job.DEFAULT_ID,
      '-1',
      source?.id ?? -1,
      'Youtube job',
      '',
      '',
      [DataDestination.Clickhouse],
      0,
      '',
      60,
      new SchedulerOnce(Date.now()),
      0,
      JobStatus.Initialized,
      JobStatus.Initialized,
      PalexyDateRange.default(),
      '',
      [],
      [],
      [],
      [],
      '',
      1000,
      SyncMode.FullSync
    );
  }

  setOrgId(dataSource: DataSourceInfo): Job {
    this.sourceId = dataSource.id;
    return this;
  }

  get canCancel(): boolean {
    return this.isSyncing || this.isQueued || this.isCompiling;
  }

  get isSyncing(): boolean {
    return this.currentSyncStatus === JobStatus.Syncing;
  }

  get isQueued() {
    return this.currentSyncStatus === JobStatus.Queued;
  }

  get isCompiling(): boolean {
    return this.currentSyncStatus === JobStatus.Compiling;
  }

  get hasNextRunTime() {
    if (this.scheduleTime!.className === SchedulerName.Once) {
      switch (this.currentSyncStatus) {
        case JobStatus.Initialized:
        case JobStatus.Queued:
          return true;
        default:
          return false;
      }
    } else {
      return true;
    }
  }

  get wasRun() {
    return this.lastSuccessfulSync > 0;
  }

  get lakeDirectory(): string {
    return `/data/db/`;
  }

  get isShowLakeConfig(): boolean {
    return true;
  }

  copyWithDestDbName(dbName: string): Job {
    this.destDatabaseName = dbName;
    return this;
  }

  withDisplayName(displayName: string): Job {
    this.displayName = displayName;
    return this;
  }

  toMultiJobs(): Job[] {
    return YOUTUBE_TABLE_INFOS.map(tableInfo => {
      const newTableInfo = cloneDeep(tableInfo);
      const job = cloneDeep(this);
      job.displayName = `${job.displayName} (table name: ${newTableInfo.tableType})`;
      job.tableName = newTableInfo.tableType;
      job.metrics = newTableInfo.metrics;
      job.dimensions = newTableInfo.dimensions;
      job.filters = newTableInfo.filters;
      job.sorts = newTableInfo.sorts;
      job.maxResults = newTableInfo.maxResults;
      return job;
    });
  }
}
