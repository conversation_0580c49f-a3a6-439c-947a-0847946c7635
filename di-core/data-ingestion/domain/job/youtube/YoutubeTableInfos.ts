interface YoutubeTableInfo {
  tableType: string;
  metrics: string[];
  dimensions: string[];
  filters: string[];
  sorts: string[];
  maxResults: number;
}

export const YOUTUBE_TABLE_INFOS: YoutubeTableInfo[] = [
  {
    tableType: 'channel',
    dimensions: ['day'],
    metrics: ['views', 'estimatedMinutesWatched', 'averageViewDuration', 'averageViewPercentage', 'subscribersGained'],
    filters: [],
    sorts: [],
    maxResults: 1000
  },
  {
    tableType: 'video',
    dimensions: ['video'],
    metrics: ['views', 'estimatedMinutesWatched', 'likes', 'subscribersGained'],
    filters: [],
    sorts: ['-views'],
    maxResults: 10
  },
  {
    tableType: 'playlist',
    dimensions: ['day'],
    metrics: ['views', 'playlistStarts', 'estimatedMinutesWatched', 'viewsPerPlaylistStart'],
    filters: [],
    sorts: [],
    maxResults: 1000
  }
];
