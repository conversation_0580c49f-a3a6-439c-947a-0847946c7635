import { KafkaStreamingJob } from '@core/data-ingestion';
import { UserProfile } from '@core/common/domain';

export class StreamingJobResponse {
  constructor(public job: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ng<PERSON><PERSON>, public creator: UserProfile) {}
  static fromObject(obj: StreamingJobResponse) {
    return new StreamingJobResponse(KafkaStreamingJob.fromObject(obj.job), obj.creator ? UserProfile.fromObject(obj.creator) : UserProfile.unknown());
  }
}
