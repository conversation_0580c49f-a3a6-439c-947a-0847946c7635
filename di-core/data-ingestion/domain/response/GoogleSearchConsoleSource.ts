import { DataSources } from '@core/data-ingestion';
import { DataSource } from '@core/data-ingestion/domain/response/DataSource';
import { SourceId } from '@core/common/domain';

export class GoogleSearchConsoleSource implements DataSource {
  readonly className = DataSources.GoogleSearchConsole;
  id: SourceId;
  orgId: string;
  displayName: string;
  refreshToken: string;
  accessToken: string;
  lastModify: number;

  constructor(id: SourceId, orgId: string, displayName: string, refreshToken: string, accessToken: string, lastModify: number) {
    this.id = id;
    this.orgId = orgId;
    this.displayName = displayName;
    this.refreshToken = refreshToken;
    this.accessToken = accessToken;
    this.lastModify = lastModify;
  }

  static fromObject(obj: any): GoogleSearchConsoleSource {
    return new GoogleSearchConsoleSource(obj.id, obj.orgId, obj.displayName, obj.refreshToken, obj.accessToken, obj.lastModify);
  }
}
