import { SourceId } from '@core/common/domain';
import { DataSources } from '@core/data-ingestion';
import { DataSource } from '@core/data-ingestion/domain/response/DataSource';

export class YoutubeSource implements DataSource {
  readonly className = DataSources.Youtube;
  id: SourceId;
  orgId: string;
  displayName: string;
  refreshToken: string;
  lastModify: number;

  constructor(id: SourceId, orgId: string, displayName: string, refreshToken: string, lastModify: number) {
    this.id = id;
    this.orgId = orgId;
    this.displayName = displayName;
    this.refreshToken = refreshToken;
    this.lastModify = lastModify;
  }

  static fromObject(obj: any): YoutubeSource {
    return new YoutubeSource(obj.id, obj.orgId, obj.displayName, obj.refreshToken, obj.lastModify);
  }
}
