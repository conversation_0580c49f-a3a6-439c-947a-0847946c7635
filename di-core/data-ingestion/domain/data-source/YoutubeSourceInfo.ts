import { SourceId } from '@core/common/domain';
import { YoutubeSource } from '@core/data-ingestion';
import { DataSourceType } from '@core/data-ingestion/domain/data-source/DataSourceType';
import { DataSources } from '@core/data-ingestion/domain/data-source/DataSources';
import { DataSource } from '@core/data-ingestion/domain/response/DataSource';
import { DataSourceInfo } from './DataSourceInfo';

export class YoutubeSourceInfo implements DataSourceInfo {
  className = DataSources.Youtube;
  sourceType = DataSourceType.Youtube;
  id: SourceId;
  orgId: string;
  displayName: string;
  refreshToken: string;
  lastModify: number;

  constructor(id: SourceId, orgId: string, displayName: string, refreshToken: string, lastModify: number) {
    this.id = id;
    this.orgId = orgId;
    this.displayName = displayName;
    this.lastModify = lastModify;
    this.refreshToken = refreshToken;
  }

  static default(): YoutubeSourceInfo {
    return new YoutubeSourceInfo(-1, '-1', '', '', 0);
  }

  static fromObject(obj: any): YoutubeSourceInfo {
    return new YoutubeSourceInfo(obj.id, obj.orgId ?? '-1', obj.displayName ?? '', obj.refreshToken ?? '', obj.lastModify ?? Date.now());
  }

  toDataSource(): DataSource {
    return new YoutubeSource(this.id, this.orgId, this.displayName, this.refreshToken, this.lastModify);
  }

  getDisplayName(): string {
    return this.displayName;
  }

  setRefreshToken(refreshToken: string): void {
    this.refreshToken = refreshToken;
  }
}
