import { BaseClient } from '@core/common/services';
import { DIKeys } from '@core/common/modules';
import { InjectValue } from 'typescript-ioc';

export abstract class LLMRepository {
  abstract generateChart(messages: string): Promise<string>;
}

export class LLMRepositoryImpl extends LLMRepository {
  @InjectValue(DIKeys.BiClient)
  private httpClient!: BaseClient;

  generateChart(messages: string): Promise<string> {
    return this.httpClient.post<string>(`/assistant/chart`, { user_message: messages }, void 0, void 0, res => res);
  }
}
