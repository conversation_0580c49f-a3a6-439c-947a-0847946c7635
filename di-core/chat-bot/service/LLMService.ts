import { Inject } from 'typescript-ioc';
import { LLMRepository } from '../repository/LLMRepository';
export abstract class LLMService {
  abstract generateChart(messages: string): Promise<string>;
}

export class LLMServiceImpl implements LLMService {
  @Inject
  private llmRepository!: LLMRepository;

  generateChart(messages: string): Promise<string> {
    return this.llmRepository.generateChart(messages);
  }
}

export class MockLLMService extends LLMService {
  generateChart(messages: string): Promise<string> {
    return Promise.resolve(
      `
I'll create a pie chart showing total profit by region using the sales data.

\`\`\`
{
  "chart_type": "pie",
  "query_setting": {
    "filters": [],
    "sorts": [],
    "legend": {
      "name": "Region",
      "function": {
        "field": {
          "class_name": "table_field",
          "db_name": "sample",
          "tbl_name": "sales",
          "field_name": "Region",
          "field_type": "string"
        },
        "class_name": "group"
      }
    },
    "value": {
      "name": "Total_Profit",
      "function": {
        "field": {
          "class_name": "table_field",
          "db_name": "sample",
          "tbl_name": "sales",
          "field_name": "Total_Profit",
          "field_type": "double"
        },
        "class_name": "sum"
      }
    },
    "class_name": "pie_chart_setting"
  }
}
\`\`\`
      `
    );
  }
}
