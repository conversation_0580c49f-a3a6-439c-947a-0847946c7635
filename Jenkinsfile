pipeline {
  agent any

  options {
    gitLabConnection('gitlab_connection')
  }

  environment {
    DOCKER_REGISTRY_HOST = 'https://registry.gitlab.com'
    DOCKER_REGISTRY_CREDENTIAL = 'acc_nkt165'
    IMAGE_NAME = 'registry.gitlab.com/datainsider/rocketbi_v2/rocket-bi-web'
  }

  stages {
    stage(test) {
      steps {
        gitlabCommitStatus('test') {
          echo 'testing...'
          sh 'yarn install --frozen-lockfile && yarn test'
        }
      }
    }
    stage('build') {
      when {
        anyOf {
          branch 'main'
          branch 'dev'
          branch 'v*'
          buildingTag()
        }
      }

      steps {
        gitlabCommitStatus('build') {
          script {
            echo "building docker image from branch ${BRANCH_NAME}..."
            docker.withRegistry(DOCKER_REGISTRY_HOST, DOCKER_REGISTRY_CREDENTIAL) {
              def image = docker.build("$IMAGE_NAME:$BRANCH_NAME")
              image.push()
            }
          }
        }
      }
    }
  }

  post {
    success {
      slackSend(
        color: 'good',
        message: "The pipeline ${currentBuild.fullDisplayName} completed successfully."
      )
    }
    failure {
      slackSend(
        color: 'danger',
        message: "The pipeline ${currentBuild.fullDisplayName} failed. Build URL: ${BUILD_URL}"
      )
    }
  }
}
