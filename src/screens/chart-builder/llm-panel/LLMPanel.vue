<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import AssistantBody from '@/shared/components/AssistantBody.vue';
import { MessageAction } from '@/screens/dashboard-detail/intefaces/chatbot/MessageAction';
import { Log } from '@core/utils';
import { ChartGenerationMetadata, LLMController, RocketBiAiController } from '@/screens/dashboard-detail/intefaces/chatbot/ChatbotController';
import { ChatMessageData } from '@/screens/dashboard-detail/intefaces/chatbot/ChatMessageData';
import { OpenAiMessageRole } from '@core/chat-bot/domain/OpenAiMessageRole';
import { MessageType } from '@/screens/dashboard-detail/intefaces/chatbot/MessageType';
import { _BuilderTableSchemaStore } from '@/store/modules/data-builder/BuilderTableSchemaStore';
import { TableSchema } from '@core/common/domain';
import EventBus from '@/screens/dashboard-detail/components/chatbot/helpers/EventBus';
import { LLMPanelEvent } from '@/screens/chart-builder/config-builder/config-panel/ConfigDraggable';
import { isString } from 'lodash';

@Component({
  components: { AssistantBody }
})
export default class LLMPanel extends Vue {
  messages: any[] = [];
  typing = false;
  visible = true;
  botOptions = {
    colorScheme: `var(--accent)`,
    msgBubbleBgUser: 'var(--accent)'
  };

  controller: LLMController = new RocketBiAiController();

  mounted() {
    EventBus.$on(LLMPanelEvent.sendErrorMessage, this.handleError);
  }

  beforeDestroy() {
    EventBus.$off(LLMPanelEvent.sendErrorMessage, this.handleError);
  }

  messageSendHandler(msg: MessageAction) {
    Log.debug('messageSendHandler::', msg);
    this.addMessage(msg);
    return this.completions();
  }

  private addMessage(msg: { text: string }) {
    const userMessage = this.buildUserMessage(msg.text);
    this.messages.push(userMessage);
  }

  private buildUserMessage(msg: string): ChatMessageData {
    return {
      role: OpenAiMessageRole.user,
      type: MessageType.text,
      text: msg
    };
  }

  private buildChartMessage(res: string): ChatMessageData {
    return {
      role: OpenAiMessageRole.assistant,
      type: MessageType.chart,
      text: res
    };
  }

  private async completions() {
    try {
      this.typing = true;
      const options: ChartGenerationMetadata = {
        table: _BuilderTableSchemaStore.tableSchemas.filter(table => table.isExpanded && table.data).map(table => table.data as TableSchema)
      };
      const message: string = await this.controller.generateChart(this.messages, options);
      this.messages.push(this.buildChartMessage(message));
    } catch (ex) {
      Log.error('FloatingChatbot::messageSendHandler::error::', ex);
      this.handleError(ex);
    } finally {
      this.typing = false;
    }
  }

  private handleError(ex: any) {
    let errorMsg: ChatMessageData | null = null;
    errorMsg = {
      role: OpenAiMessageRole.assistant,
      type: MessageType.error,
      text: isString(ex) ? ex : ex.message
    };
    errorMsg ? this.messages.push(errorMsg) : void 0;
  }
}
</script>

<template>
  <div id="llm-panel">
    <AssistantBody
      ref="assistant"
      class="di-assistant"
      v-model="visible"
      :messages="messages"
      :options="botOptions"
      :bot-typing="typing"
      @msg-send="messageSendHandler"
    />
  </div>
</template>

<style lang="scss">
#llm-panel {
  .di-assistant {
    .qkb-board-content {
      border-radius: 4px;
    }

    text-align: start;
    word-break: break-word; //Text inside bubble
    white-space: pre-line;

    textarea {
      resize: none;
      color: var(--text-color);
    }
  }
}
</style>
