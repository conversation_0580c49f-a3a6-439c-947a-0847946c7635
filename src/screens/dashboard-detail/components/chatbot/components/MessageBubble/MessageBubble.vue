<template>
  <div class="di-msg-bubble" :class="bubbleClass">
    <div class="bot-avatar" v-if="isBot">
      <LogoComponent :company-logo-url="logoUrl" height="24px" width="24px" />
    </div>
    <component v-if="componentType" :is="componentType" :main-data="message"></component>
    <div class="di-msg-bubble__time" v-if="message.createdAt">
      {{ message.createdAt }}
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import SingleText from './SingleText.vue';
import ButtonOptions from './ButtonOptions.vue';
import { ChatMessageData } from '@/screens/dashboard-detail/intefaces/chatbot/ChatMessageData';
import { OpenAiMessageRole } from '@core/chat-bot/domain/OpenAiMessageRole';
import ChartMessage from '@/screens/dashboard-detail/components/chatbot/components/MessageBubble/ChartMessage.vue';
import LogoComponent from '@/screens/organization-settings/components/organization-logo-modal/LogoComponent.vue';
import { OrganizationStoreModule } from '@/store/modules/OrganizationStore';
import ErrorMessage from '@/screens/dashboard-detail/components/chatbot/components/MessageBubble/ErrorMessage.vue';

@Component({
  components: {
    LogoComponent,
    SingleText,
    ButtonOptions,
    ChartMessage,
    ErrorMessage
  }
})
export default class MessageBubble extends Vue {
  @Prop() message!: ChatMessageData;

  protected get logoUrl(): string {
    return OrganizationStoreModule.organization.thumbnailUrl || '';
  }

  get bubbleClass() {
    return this.isBot ? 'di-msg-bubble--bot' : 'di-msg-bubble--user';
  }

  get isBot(): boolean {
    switch (this.message.role) {
      case OpenAiMessageRole.user:
        return false;
      default:
        return true;
    }
  }

  get componentType() {
    const componentDisplay: Record<string, string> = {
      text: 'SingleText',
      button: 'ButtonOptions',
      chart: 'ChartMessage',
      error: 'ErrorMessage'
    };

    return componentDisplay[this.message.type] ?? 'SingleText';
  }
}
</script>

<style lang="scss">
.bot-avatar {
  width: 48px;
  height: 48px;
  aspect-ratio: 1;
  position: relative;
  background: var(--accent);
  border-radius: 100vw;
  object-fit: cover;
  .company-logo-component {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
</style>
