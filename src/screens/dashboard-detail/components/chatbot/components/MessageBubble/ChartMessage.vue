<template>
  <div class="qkb-msg-bubble-component qkb-msg-bubble-component--single-text">
    <div class="qkb-msg-bubble-component__text text-left" v-html="textDisplay"></div>
  </div>
</template>
<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import { ChatMessageData } from '@/screens/dashboard-detail/intefaces/chatbot/ChatMessageData';
import { Inject as IOCInject } from 'typescript-ioc';
import { ChartOption, DIException, QuerySetting } from '@core/common/domain';
import { JsonUtils, Log } from '@core/utils';
import { ChartType, FunctionData } from '@/shared';
import { DraggableSettingResolver } from '@/shared/resolver';
import { _ConfigBuilderStore } from '@/screens/chart-builder/config-builder/ConfigBuilderStore';
import EventBus from '@/screens/dashboard-detail/components/chatbot/helpers/EventBus';
import { ChartBuilderEvent, LLMPanelEvent } from '@/screens/chart-builder/config-builder/config-panel/ConfigDraggable';

@Component
export default class ChartMessage extends Vue {
  @Prop() mainData!: ChatMessageData;

  @IOCInject
  private factory!: DraggableSettingResolver;

  get textDisplay() {
    return this.parseResponse(this.mainData.text).text;
  }

  get chartResponse(): { querySetting: any; chartType: ChartType } | null {
    const codeBlock: string | null = this.parseResponse(this.mainData.text).codeBlock;
    if (!codeBlock) {
      return null;
    }
    try {
      return JsonUtils.fromObject<{ querySetting: any; chartType: ChartType }>(codeBlock);
    } catch (ex) {
      Log.error('ChartMessage::querySetting::error::', ex);
      return null;
    }
  }

  get querySetting(): QuerySetting | null {
    if (!this.chartResponse) {
      return null;
    }

    try {
      const options = ChartOption.getDefaultChartOption(this.chartResponse.chartType);
      const querySetting: QuerySetting = this.toQuerySetting(this.chartResponse.querySetting);
      querySetting.setChartOption(options);
      return querySetting;
    } catch (ex) {
      Log.error('ChartMessage::querySetting::error::', ex);
      return null;
    }
  }

  mounted() {
    if (!this.chartResponse || !this.chartResponse.chartType || !this.chartResponse.querySetting) {
      return;
    }

    this.emitDraggableSetting(this.chartResponse!.chartType, this.querySetting);
  }

  private emitDraggableSetting(chartType: ChartType, querySetting: QuerySetting | null) {
    if (!querySetting) {
      EventBus.$emit(LLMPanelEvent.sendErrorMessage, 'Invalid query setting');
      return;
    }
    const { configsAsMap, filterAsMap } = this.factory.toDraggableSetting(chartType, querySetting);
    _ConfigBuilderStore.setSelectedChartType(chartType);
    _ConfigBuilderStore.setChartOption(querySetting.getChartOption()!);
    _ConfigBuilderStore.setConfigs(Object.fromEntries(configsAsMap) as Record<string, FunctionData[]>);
    Log.debug(`filterAsMap`, filterAsMap);
    _ConfigBuilderStore.setFilter(filterAsMap);
    EventBus.$emit(ChartBuilderEvent.onConfigChanged);
  }

  // private submitChartSetting(response: string) {
  //   this.factory.ensureResponse(request.chartType, response);
  //   return this.factory.parse(request.chartType, response);
  // }

  private parseResponse(response: string): { text: string; codeBlock: string | null } {
    const codeBlockRegex = /```([\s\S]*?)```/;
    const match = response.match(codeBlockRegex);

    const text = response.replace(/```[\s\S]*?```/g, '').trim();
    const codeBlock = match ? match[1].trim() : null;

    return { text, codeBlock };
  }

  private toQuerySetting(querySettingObj: any): QuerySetting {
    const result = QuerySetting.fromObject(querySettingObj);
    if (!result) {
      throw new DIException('Invalid query setting');
    }

    return result;
  }
}
</script>
