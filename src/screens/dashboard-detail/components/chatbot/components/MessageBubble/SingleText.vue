<template>
  <div class="di-msg-bubble-component di-msg-bubble-component--single-text">
    <div class="di-msg-bubble-component__text" v-html="mainData.text"></div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import { ChatMessageData } from '@/screens/dashboard-detail/intefaces/chatbot/ChatMessageData';

@Component
export default class SingleText extends Vue {
  @Prop() mainData!: ChatMessageData;
}
</script>
