<template>
  <div class="di-msg-bubble-component di-msg-bubble-component--error">
    <div class="di-msg-bubble-component__text" v-html="mainData.text"></div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import { ChatMessageData } from '@/screens/dashboard-detail/intefaces/chatbot/ChatMessageData';

@Component
export default class ErrorMessage extends Vue {
  @Prop() mainData!: ChatMessageData;
}
</script>

<style lang="scss" scoped>
.di-msg-bubble-component--error {
  .di-msg-bubble-component__text {
    color: var(--danger);
    border: 2px solid red;
    background-color: rgba(234, 107, 107, 0.1);
  }
}
</style>
