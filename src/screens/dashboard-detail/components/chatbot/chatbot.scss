@import '~bootstrap/scss/functions';
@import '~bootstrap/scss/variables';
@import '~bootstrap/scss/mixins';

// Variables
$bot-zindex: 9999;
$font-size-root: 14px;

$board-width: 370px;
$board-radius: 8px;
$board-bg: #fff;

$action-height: 46px;
$action-line: #e0e0e0;
$action-input-bg: transparent;
$action-icon-color: #999;

$bubble-btn-color: #fff;
$bubble-time-color: #fff;
$bubble-time-bg: rgba(0, 0, 0, 0.3);

// Main container
.di-chatbot-ui {
  position: fixed;
  z-index: $bot-zindex;
  bottom: 1.5rem;
  right: 1.5rem;
  display: flex;
  flex-direction: column;
  font-size: $font-size-root;

  * {
    box-sizing: border-box;
  }
}

.di-preload-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 0;
  overflow: hidden;
}

// Animations
.di-chatbot-ui--animate {
  // FadeUp
  .di-fadeUp-enter-active,
  .di-fadeUp-leave-active {
    opacity: 1;
    transform: translate(0, 0);
    transition: opacity 0.15s linear, transform 0.2s ease-out;
  }

  .di-fadeUp-enter,
  .di-fadeUp-leave-to {
    transform: translate(0, 20px);
    opacity: 0;
  }

  // ScaleUp
  .di-scaleUp-enter-active,
  .di-scaleUp-leave-active {
    opacity: 1;
    transform: scale(1, 1);
    transition: opacity 0.1s linear, transform 0.2s ease-out;
  }

  .di-scaleUp-enter,
  .di-scaleUp-leave-to {
    transform: scale(0, 0);
    opacity: 0;
  }
}

// Typing indicator
@keyframes diJump {
  from {
    transform: translateY(2px);
  }
  to {
    transform: translateY(-1px);
  }
}

$typing-duration: 0.35s;
$typing-indicator-size: 7px;

.di-msg-bubble__typing-indicator {
  position: relative;
  min-width: $typing-indicator-size * 3 + 8px;
  opacity: 0.3;

  span {
    display: block;
    width: $typing-indicator-size;
    height: $typing-indicator-size;
    margin: 0 auto;
    border-radius: 50%;
    background-color: currentColor;
  }

  &::before,
  &::after {
    content: '';
    display: block;
    position: absolute;
    top: 0;
    width: $typing-indicator-size;
    height: $typing-indicator-size;
    border-radius: 50%;
    background-color: currentColor;
  }

  &::before {
    left: 0;
  }

  &::after {
    right: 0;
  }

  span,
  &::before,
  &::after {
    transform: translateY(2px);
    animation: diJump ease $typing-duration infinite alternate;
  }

  span {
    animation-delay: $typing-duration / 2;
  }

  &::after {
    animation-delay: $typing-duration;
  }
}

// Bubble button
.di-bot-bubble {
  align-self: flex-end;
}

.di-bubble-btn {
  display: block;
  padding: 0;
  outline: 0;
  border: 0;
  box-shadow: none;
  border-radius: 50%;
  color: $bubble-btn-color;
  fill: $bubble-btn-color;
  cursor: pointer;
  box-shadow: 0 3px 5px 0 rgba(0, 0, 0, 0.15);
  transition: opacity linear 0.2s;

  &:hover {
    opacity: 0.85;
  }
}

.di-bubble-btn-icon {
  display: block;
  position: absolute;
  top: 50%;
  left: 50%;
  width: 27px;
  height: auto;
  margin: -11px 0 0 -13px;
  line-height: 1em;

  &.di-bubble-btn-icon--close {
    width: 15px;
    margin: -7px 0 0 -7px;
  }
  &.di-bubble-btn-icon--open {
    top: 26px;
    left: 27px;
  }
}

// Board
.di-board {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
  margin-bottom: 10px;
  border-radius: $board-radius;
  background-color: $board-bg;
  box-shadow: 0 3px 30px 0 rgba(0, 0, 0, 0.15);
}

// Board Header
.di-board-header {
  flex: none;
  padding: 1rem;
  border-bottom: 1px solid #e0e0e0;
}

.di-board-header__title {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
}

// Board Content
.di-board-content {
  flex-grow: 1;
  overflow: hidden scroll;
}

.di-board-content__bubbles {
  min-height: 100%;
  padding: 0.5rem 1rem;
}

// Message Bubble Components
.di-msg-bubble {
  display: flex;
  position: relative;
  padding-bottom: 1rem;

  &.di-msg-bubble--bot {
    .di-msg-bubble-component {
      margin-right: 2.5rem;
      margin-left: 0.5rem;
    }
  }

  &.di-msg-bubble--user {
    justify-content: flex-end;

    .di-msg-bubble-component {
      margin-left: 5rem;
    }
  }
}

.di-msg-avatar {
  flex-grow: 1;
  flex: none;
  position: relative;
  overflow: hidden;
  border-radius: 50%;
}

.di-msg-avatar__img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  background-size: cover;
  background-repeat: no-repeat;
}

.di-msg-bubble__time {
  display: none;
  position: absolute;
  right: 0;
  top: 0;
  padding: 2px 5px;
  margin-top: -5px;
  border-radius: 5px;
  font-size: 0.625rem;
  color: $bubble-time-color;
  background-color: $bubble-time-bg;
  transform: translate(0, -100%);
  opacity: 0;
  transition: opacity linear 0.1s 1s;

  .di-msg-bubble--user & {
    display: block;
  }
}

.di-msg-bubble-component {
  font-size: 0.875rem;

  &:hover {
    & ~ .di-msg-bubble__time {
      opacity: 0.8;
    }
  }
}

.di-msg-bubble-component__text {
  position: relative;
  padding: 12px;
  border-radius: 6px;
}

.di-msg-bubble-component__options-wrapper {
  display: flex;
  flex-wrap: wrap;
}

.di-mb-button-options__item {
  flex: 0 0 auto;
}

.di-mb-button-options__btn {
  display: block;
  overflow: hidden;
  position: relative;
  margin: 0.5rem 0.375rem 0 0;
  padding: 0.25rem 1rem;
  cursor: pointer;
  outline: 0;
  border: 1px solid transparent;
  border-radius: 13px;
  color: inherit;
  font-size: 0.875rem;
  font-family: inherit;
  text-decoration: none;
  background-color: transparent;
  transition: background-color linear 0.15s, color linear 0.1s;

  span {
    position: relative;
    z-index: 10;
  }
}

// Board Action
.di-board-action {
  position: relative;
  display: flex;
  flex: none;
  height: $action-height;
  padding: 0.45rem 1.25rem 0.5rem;
  border-top: 1px solid $action-line;
}

.di-board-action--disabled {
  &::before {
    content: '';
    display: block;
    position: absolute;
    z-index: 10;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.2;
    cursor: not-allowed;
  }
}

.di-board-action__wrapper {
  display: flex;
  width: 100%;
  background-color: $action-input-bg;
}

.di-board-action__msg-box {
  position: relative;
  flex-grow: 1;
  padding: 0 1rem 0 0;
}

.di-board-action__input {
  height: 100%;
  width: 100%;
  padding: 0;
  font-size: 0.875rem;
  border: 0;
  background-color: transparent;
  box-shadow: none;
  outline: 0;

  &[disabled='disabled'] {
    pointer-events: none;
  }
}

.di-board-action__disable-text {
  display: flex;
  align-items: center;
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background-color: $action-input-bg;
  font-size: 0.875rem;
}

.di-board-action__extra {
  display: flex;
  flex: none;
  justify-content: center;
}

.di-action-item {
  display: block;
  padding: 0;
  outline: 0;
  border: 0;
  line-height: 1;
  box-shadow: none;
  background-color: transparent;
  cursor: pointer;
  opacity: 0.6;
  transition: opacity linear 0.1s;

  &:hover {
    opacity: 1;
  }
}

.di-action-icon {
  width: 16px;
  height: 16px;
  line-height: 1;
  fill: $action-icon-color;
  color: $action-icon-color;
  vertical-align: middle;
}

.di-board-action--typing {
  .di-action-item--send {
    opacity: 1;
  }
}
