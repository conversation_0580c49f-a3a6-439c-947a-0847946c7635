{"name": "rocket-bi", "version": "v23.12.22", "private": true, "description": "RocketBI project", "scripts": {"serve": "yarn && vue-cli-service serve --open", "build": "export NODE_OPTIONS='--max-old-space-size=4096' && vue-cli-service build", "lint": "export NODE_OPTIONS='--max-old-space-size=4096' && vue-cli-service lint --fix .", "start": "npx serve -s dist -p 8080", "test": "export NODE_OPTIONS='--max-old-space-size=4096' && vue-cli-service test:unit", "create:index": "./create_file_index.sh"}, "dependencies": {"@johmun/vue-tags-input": "^2.1.0", "@vue/composition-api": "^1.7.1", "ant-design-vue": "^1.7.2", "axios": "^0.19.2", "bootstrap-vue": "^2.15.0", "color": "^4.2.3", "color-scales": "^3.0.2", "comlink": "^4.3.0", "core-js": "^3.6.5", "deneric": "^1.0.6-beta", "di-web-analytics": "^0.8.3", "draggabilly": "^2.3.0", "file-saver": "^2.0.5", "gridstack": "2.2.0", "highcharts": "^11.1.0", "highcharts-custom-events": "^3.0.10", "highcharts-vue": "^1.4.3", "ion-rangeslider": "^2.3.1", "js-cookies": "^1.0.4", "js-sha256": "0.9.0", "konva": "7", "leader-line-new": "^1.1.9", "lodash": "^4.17.15", "moment": "^2.29.0", "monaco-editor": "^0.19.3", "monaco-editor-vue": "^1.0.10", "monaco-editor-webpack-plugin": "1.9.1", "nprogress": "^0.2.0", "path": "^0.12.7", "sweetalert2": "^11.0.18", "tui-image-editor": "3.15.2", "typescript-ioc": "^3.2.2", "v-calendar": "^1.0.8", "v-click-outside": "^3.0.1", "vietnamese-js": "^1.0.0", "vue": "^2.6.11", "vue-class-component": "^7.2.3", "vue-click-outside": "^1.1.0", "vue-clipboard2": "^0.3.1", "vue-color": "^2.7.1", "vue-context": "^6.0.0", "vue-drag-drop": "^1.1.4", "vue-draggable-resizable": "^2.2.0", "vue-gpickr": "0.4.2", "vue-property-decorator": "8.2.2", "vue-recaptcha": "^2.0.3", "vue-router": "^3.4.3", "vue-select": "^3.10.8", "vue-split-panel": "^1.0.4", "vue-virtual-scroller": "^1.0.10", "vue2-highcharts": "^1.2.5", "vue2-transitions": "^0.3.0", "vuedraggable": "^2.24.3", "vuelidate": "^0.7.5", "vuescroll": "4.17.3", "vuex": "^3.4.0", "vuex-class": "^0.3.2"}, "devDependencies": {"@types/chai": "^4.2.11", "@types/color": "^3.0.1", "@types/crypto-js": "^4.0.1", "@types/draggabilly": "^2.1.3", "@types/file-saver": "^2.0.1", "@types/gapi": "^0.0.39", "@types/gapi.auth2": "^0.0.52", "@types/gapi.client": "^1.0.5", "@types/gapi.client.analytics": "^3.0.7", "@types/gapi.client.analyticsadmin": "^1.0.1", "@types/gapi.client.analyticsdata": "^1.0.0", "@types/gapi.client.analyticsreporting": "v4", "@types/gapi.client.drive": "^3.0.13", "@types/gapi.client.sheets": "^4.0.20201029", "@types/gapi.client.webmasters": "^3.0.2", "@types/jquery": "^3.5.16", "@types/lodash": "^4.14.157", "@types/mocha": "^5.2.4", "@types/rx": "^4.1.2", "@types/vuelidate": "^0.7.13", "@typescript-eslint/eslint-plugin": "^2.33.0", "@typescript-eslint/parser": "^2.33.0", "@vue/cli-plugin-babel": "4.5.9", "@vue/cli-plugin-eslint": "4.5.9", "@vue/cli-plugin-router": "4.5.9", "@vue/cli-plugin-typescript": "4.5.9", "@vue/cli-plugin-unit-mocha": "4.5.9", "@vue/cli-plugin-vuex": "4.5.9", "@vue/cli-service": "4.5.9", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^5.0.2", "@vue/test-utils": "^1.0.3", "babel-core": "^6.26.3", "babel-plugin-import": "^1.13.3", "babel-plugin-jsx-v-model": "^2.0.3", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-vue-jsx": "^3.7.0", "babel-preset-latest": "^6.24.1", "bootstrap": "^4.5.0", "chai": "^4.1.2", "create-ts-index": "^1.13.6", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^6.2.2", "husky": "^3.0.5", "jquery": "^3.6.0", "lint-staged": "^9.2.5", "null-loader": "^4.0.1", "prettier": "^1.19.1", "pretty-quick": "^1.11.1", "raw-loader": "^4.0.2", "sass": "^1.26.5", "sass-loader": "^8.0.2", "serve": "^11.3.2", "typescript": "3.9.7", "typings-for-css-modules-loader": "^1.7.0", "vue-template-compiler": "^2.6.11", "vuex-module-decorators": "^0.17.0", "worker-loader": "^3.0.8", "worker-plugin": "^5.0.0"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"(src|di-core)/**/*.(js|ts|js|tsx|vue)": ["yarn lint", "pretty-quick --staged", "git add"]}}